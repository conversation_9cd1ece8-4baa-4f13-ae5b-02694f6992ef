import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/drizzleConfigCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';

export const dynamic = 'force-dynamic';

interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  legacyFilters?: Record<string, any>; // 支持传统的filter参数
}

// 构建复杂的 Prisma where 条件 - 重构版本
function buildAdvancedWhere(conditions: SearchCondition[], config: DatabaseConfig): Record<string, unknown> {
  if (conditions.length === 0) {
    return {};
  }

  // 如果只有一个条件，直接构建
  if (conditions.length === 1) {
    return buildSingleCondition(conditions[0], config);
  }

  // 多个条件需要根据逻辑操作符组合
  const andConditions: Record<string, unknown>[] = [];
  const orConditions: Record<string, unknown>[] = [];
  const notConditions: Record<string, unknown>[] = [];

  conditions.forEach((condition, _index) => {
    const singleWhere = buildSingleCondition(condition, config);

    if (_index === 0) {
      // 第一个条件总是 AND
      andConditions.push(singleWhere);
    } else {
      switch (condition.logic) {
        case 'OR':
          orConditions.push(singleWhere);
          break;
        case 'NOT':
          notConditions.push(singleWhere);
          break;
        default: // 'AND'
          andConditions.push(singleWhere);
          break;
      }
    }
  });

  // 构建最终的 where 条件
  const finalConditions: Record<string, unknown>[] = [];

  if (andConditions.length > 0) {
    if (andConditions.length === 1) {
      finalConditions.push(andConditions[0]);
    } else {
      finalConditions.push({ AND: andConditions });
    }
  }

  if (orConditions.length > 0) {
    finalConditions.push({ OR: orConditions });
  }

  if (notConditions.length > 0) {
    finalConditions.push({ NOT: { OR: notConditions } });
  }

  if (finalConditions.length === 1) {
    return finalConditions[0];
  } else if (finalConditions.length > 1) {
    return { AND: finalConditions };
  }

  return {};
}

// 构建单个条件 - 使用 searchType 而不是 operator
function buildSingleCondition(condition: SearchCondition, config: DatabaseConfig): Record<string, unknown> {
  const fieldName = condition.field;
  const value = condition.value;

  // 处理特殊的全局搜索字段
  if (fieldName === 'allFields' && typeof value === 'string' && value.trim()) {
    const keyword = value.trim();
    const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

    if (searchableFields.length > 0) {
      const globalSearchConditions = searchableFields.map(f => ({
        [f.fieldName]: { contains: keyword, mode: 'insensitive' }
      }));

      return { OR: globalSearchConditions };
    }
    return {};
  }

  const fieldConfig = config.fields.find(f => f.fieldName === condition.field);
  if (!fieldConfig) {
    return {};
  }

  const searchType = fieldConfig.searchType || 'contains';

  // 处理日期范围类型
  if (searchType === 'date_range' && typeof value === 'object' && !Array.isArray(value) && value.from && value.to) {
    return {
      [fieldName]: {
        gte: new Date(value.from),
        lte: new Date(value.to),
      }
    };
  }

  // 处理单个日期
  if (fieldConfig.fieldType === 'date' && typeof value === 'string' && value) {
    const date = new Date(value);
    return { [fieldName]: date };
  }

  // 处理多选字段
  if (Array.isArray(value) && value.length > 0) {
    return { [fieldName]: { in: value } };
  }

  // 处理布尔类型
  if (fieldConfig.fieldType === 'boolean' && typeof value === 'string') {
    return { [fieldName]: value === 'true' };
  }

  // 处理文本类型
  if (typeof value === 'string' && value.trim()) {
    const trimmedValue = value.trim();

    // 处理N/A值
    if (trimmedValue === 'N/A') {
      return {
        OR: [
          { [fieldName]: null },
          { [fieldName]: '' }
        ]
      };
    }

    // 根据 searchType 处理文本值
    switch (searchType) {
      case 'exact':
        return { [fieldName]: { equals: trimmedValue, mode: 'insensitive' } };
      case 'contains':
      default:
        return { [fieldName]: { contains: trimmedValue, mode: 'insensitive' } };
    }
  }

  return {};
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查 - 临时禁用用于测试
    /*
    const requiredLevel = getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }
    */

    const body: AdvancedSearchRequest = await request.json();
    const { conditions, sortBy, sortOrder = 'desc', legacyFilters = {} } = body;

    console.log('[Advanced Search API] 接收到的参数:', {
      conditions: conditions.length,
      legacyFilters: Object.keys(legacyFilters).length,
      sortBy,
      sortOrder
    });

    // 使用全局翻页配置（性能优化）
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);

    // 使用已获取的配置
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建复杂查询条件 - 支持Advanced Search条件和legacy filters
    let where = buildAdvancedWhere(conditions, config);

    // 如果有legacy filters，需要将其转换并合并到where条件中
    if (Object.keys(legacyFilters).length > 0) {
      console.log('[Advanced Search API] 处理legacy filters:', legacyFilters);

      // 将legacy filters转换为URLSearchParams格式
      const searchParams = new URLSearchParams();
      Object.entries(legacyFilters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.set(key, String(value));
        }
      });

      // 使用buildDrizzleWhere处理legacy filters
      const { buildDrizzleWhere } = await import('@/lib/server/buildDrizzleWhere');
      const legacyWhere = buildDrizzleWhere(searchParams, config, {});

      // 合并Advanced Search条件和legacy条件
      if (where && legacyWhere) {
        where = { AND: [where, legacyWhere] };
      } else if (legacyWhere) {
        where = legacyWhere;
      }
    }

    // 构建排序条件
    const defaultSortField = sortableFields[0] || 'approvalDate';
    const orderBy = {
      [sortBy || defaultSortField]: sortOrder,
    };

    // 使用动态模型获取
    const model = await getDynamicTable(database);
    if (!isDrizzleTable(model)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 构建 select 对象
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    // 主键id始终返回，但不再返回database字段
    select['id'] = true;

    const data = await (model as any).findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];
    
    const totalCount = await (model as any).count({ where }) as number;

    return NextResponse.json({
      success: true,
      data,
      pagination: buildPaginationResponse(page, limit, totalCount),
      conditions,
      config,
      databaseInfo: {
        code: database,
        originalCode: rawDatabase,
      }
    });
    
  } catch (__error) {
    console.error('Advanced search API error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
