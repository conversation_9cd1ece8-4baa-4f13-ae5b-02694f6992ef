"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MultiSelect } from "@/components/ui/multi-select";
import { Filter, Plus, X, Search } from "lucide-react";

export interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchProps {
  onSearch: (conditions: SearchCondition[]) => void;
  onClear: () => void;
  availableFields: Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
    filterType?: string;
    searchType?: string;
    isAdvancedSearchable?: boolean;
  }>;
  currentConditions?: SearchCondition[];
  metadata?: Record<string, string[]>;
  metadataWithCounts?: Record<string, Array<{ value: string; count: number }>>;
}

const LOGIC_OPERATORS = [
  { value: 'AND', label: 'AND' },
  { value: 'OR', label: 'OR' },
  { value: 'NOT', label: 'NOT' },
];

export default function AdvancedSearch({
  onSearch,
  onClear,
  availableFields,
  currentConditions = [],
  metadata = {},
  metadataWithCounts = {}
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>(currentConditions);

  // Filter to only show fields that are marked as advanced searchable
  const advancedSearchableFields = availableFields.filter(field => field.isAdvancedSearchable);

  const addCondition = () => {
    const newCondition: SearchCondition = {
      id: Date.now().toString(),
      field: advancedSearchableFields[0]?.fieldName || '',
      value: '',
      logic: conditions.length > 0 ? 'AND' : undefined,
    };
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (id: string) => {
    const newConditions = conditions.filter(c => c.id !== id);
    // Remove logic from first condition if it exists
    if (newConditions.length > 0 && newConditions[0].logic) {
      newConditions[0] = { ...newConditions[0], logic: undefined };
    }
    setConditions(newConditions);
  };

  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setConditions(conditions.map(c =>
      c.id === id ? { ...c, ...updates } : c
    ));
  };

  const handleSearch = () => {
    const validConditions = conditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (Array.isArray(c.value)) {
        return c.value.length > 0;
      }
      if (typeof c.value === "object" && c.value !== null) {
        return c.value.from || c.value.to;
      }
      return false;
    });
    onSearch(validConditions);
    setIsOpen(false);
  };

  const handleClear = () => {
    setConditions([]);
    onClear();
    setIsOpen(false);
  };



  const renderValueInput = (condition: SearchCondition, field: Record<string, unknown>) => {
    // Handle date range fields
    if (field.fieldType === "date" || field.searchType === 'date_range') {
      const dateValue = typeof condition.value === "object" && !Array.isArray(condition.value) ? condition.value : { from: '', to: '' };
      return (
        <DateRangePicker
          startDate={dateValue.from || ''}
          endDate={dateValue.to || ''}
          onStartDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, from: date }
          })}
          onEndDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, to: date }
          })}
          placeholder="Select date range"
          className="w-full"
        />
      );
    }

    // Handle multi-select fields
    if (field.filterType === 'multi_select') {
      // 优先使用带统计数据的metadata，如果没有则回退到普通metadata
      const optionsWithCounts = metadataWithCounts[field.fieldName as string] || [];
      const options = metadata[field.fieldName as string] || [];

      // 如果有统计数据，使用统计数据；否则使用普通选项
      const finalOptions = optionsWithCounts.length > 0
        ? optionsWithCounts.map(item => ({
            value: item.value,
            label: item.value,
            count: item.count
          }))
        : options
            .filter((option: unknown) => typeof option === 'string' && option.trim() !== '')
            .map((option: unknown) => ({
              value: String(option),
              label: String(option)
            }));

      return (
        <MultiSelect
          options={finalOptions}
          value={Array.isArray(condition.value) ? condition.value : []}
          onValueChange={(value) => updateCondition(condition.id, { value })}
          placeholder={`Select ${field.displayName}`}
          className="w-full"
        />
      );
    }

    // Handle boolean fields
    if (field.fieldType === 'boolean') {
      return (
        <Select
          value={typeof condition.value === "string" ? condition.value : '__none__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value === "__none__" ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__none__">Select value</SelectItem>
            <SelectItem value="true">True</SelectItem>
            <SelectItem value="false">False</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    // Handle single select fields
    if (field.fieldType === "select" || field.filterType === 'select') {
      // 优先使用带统计数据的metadata，如果没有则回退到普通metadata
      const optionsWithCounts = metadataWithCounts[field.fieldName as string] || [];
      const options = (metadata as any)[field.fieldName as string] || [];

      // 如果有统计数据，使用统计数据；否则使用普通选项
      const finalOptions = optionsWithCounts.length > 0
        ? optionsWithCounts
        : options
            .filter((option: unknown) => typeof option === 'string' && option.trim() !== '')
            .map((option: unknown) => ({
              value: String(option),
              count: 0 // 没有统计数据时设为0
            }));

      return (
        <Select
          value={typeof condition.value === "string" ? condition.value : '__all__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value === "__all__" ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`Select ${field.displayName}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__all__">All</SelectItem>
            {finalOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.value} {option.count > 0 && `(${option.count})`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    // Default to text input
    return (
      <Input
        placeholder="Enter value"
        value={typeof condition.value === "string" ? condition.value : ''}
        onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
        className="w-full"
      />
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 text-xs">
          <Filter className="mr-1 h-3 w-3" />
          Advanced Search
          {currentConditions.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-4 text-xs">
              {currentConditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Search</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {conditions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search conditions added yet.</p>
              <p className="text-sm">Click &quot;Add Condition&quot; to start building your search.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {conditions.map((condition, _index) => {
                const field = advancedSearchableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      {_index > 0 && (
                        <Select
                          value={condition.logic || 'AND'}
                          onValueChange={(value) => updateCondition(condition.id, {
                            logic: value as 'AND' | 'OR' | 'NOT'
                          })}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {LOGIC_OPERATORS.map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      <Button
                        variant="ghost" size="sm" onClick={() => removeCondition(condition.id)}
                        className="ml-auto text-red-600 hover:text-red-800 hover:bg-red-50">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex flex-col lg:flex-row gap-3 items-start">
                      {/* Field Selection */}
                      <div className="w-full lg:w-48">
                        <label className="block text-sm font-medium mb-1">Field</label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, { field: value })}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {advancedSearchableFields.map(field => (
                              <SelectItem key={field.fieldName} value={field.fieldName}>
                                {field.displayName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Value Input */}
                      <div className="flex-1 min-w-0">
                        <label className="block text-sm font-medium mb-1">Value</label>
                        {renderValueInput(condition, field)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline" onClick={addCondition}
              className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" onClick={handleClear}>
                Clear All
              </Button>
              <Button onClick={handleSearch} disabled={conditions.length === 0}>
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
